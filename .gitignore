# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
#*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

##################
# NEW IGNORE FILES

# Config Files
*.ini
.DS_Store

# Binary Files
*.dat

# Addition
# Ignore all temporary files in the root directory
*.[oa]

# Single Module Project
/.externalToolBuilders/
/.settings/
/.metadata/
/.recommenders/
/.allure/
/target/
/bin/
/build/
/doc/
##/lib/
/test-output/
/allure-results/
/.classpath
/.project
/.checkstyle
/reports/
/.idea/
/out/
/shelf/
/resources/static/
/resources/front/
workspace.xml
*.iml
*.ucls

# Multi Modules Project
/**/.externalToolBuilders/
/**/.settings/
/**/.allure/
/**/target/
/**/doc/
/**/bin/
/**/build/
##/**/lib/
/**/test-output/
/**/allure-results/
/**/report/
/**/.classpath
/**/.project
/**/.checkstyle
/**/reports
/**/af.reporting
/**/.idea/
/**/out/
/**/shelf/
/**/workspace.xml
/**/*.iml
/**/*.ucls

/**/frontend/static/
/**/frontend/*.png
/**/frontend/*.json
/**/frontend/*.ico
/**/frontend/*.html
/**/frontend/robots.txt

# Ignore binary
*.bin
##*.exe
*.xpi

# Ignore log
# *.log
*.log.[1234567890]

# Ignore IntelliJ IDEA Config Files
*.stackdump

# Ignore Picture
*.bmp
# *.png
#*.jpg
*.jpeg

# Ignore doc/notes.txt, but not doc/info/arch.txt
# doc/*.txt

# Ignore all .txt files in the directory doc/
# doc/**/*.txt

# Ignore .doc .pfd .docx
*.doc
*.docx
*.pdf

# bower
/bower_components/
/**/bower_components/

# npm
/node_modules/
/**/node_modules/

# Visual Studio Code
/.vscode/
